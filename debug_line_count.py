#!/usr/bin/env python3
"""
Debug the line count parsing issue.
"""

import re

def test_line_count_parsing():
    """Test the regex patterns for line count parsing."""
    
    test_goal = "Create 3 Python files, each containing exactly 20 lines of code"
    
    print(f"Testing goal: '{test_goal}'")
    
    # Test the UPDATED regex patterns from the agent
    multi_file_patterns = [
        # Enhanced patterns to capture line counts better
        r'create\s+(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?.*?(?:exactly\s+)?(\d+)\s+lines?',
        r'(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?.*?(?:exactly\s+)?(\d+)\s+lines?',
        r'create\s+(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
        r'(\d+|one|two|three|four|five|six|seven|eight|nine|ten)\s+(.+?)\s*files?(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?',
        r'create\s+(.+?)\s*files?\s*-\s*(\d+|one|two|three|four|five|six|seven|eight|nine|ten)(?:\s+each\s+with\s+(\d+)\s+lines?)?(?:\s+named\s+(.+))?'
    ]
    
    for i, pattern in enumerate(multi_file_patterns, 1):
        print(f"\nPattern {i}: {pattern}")
        match = re.search(pattern, test_goal, re.IGNORECASE)
        if match:
            print(f"  ✅ MATCH: {match.groups()}")
            groups = match.groups()
            if len(groups) >= 2:
                num_files_str = groups[0].lower() if groups[0] else '1'
                file_description_base = groups[1].strip() if groups[1] else 'python file'
                lines_str = groups[2] if len(groups) > 2 and groups[2] else None
                naming_pattern = groups[3] if len(groups) > 3 and groups[3] else None
                
                print(f"  Parsed:")
                print(f"    num_files_str: '{num_files_str}'")
                print(f"    file_description_base: '{file_description_base}'")
                print(f"    lines_str: {lines_str}")
                print(f"    naming_pattern: {naming_pattern}")
                
                # Test the number mapping
                num_map = {'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5, 'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10}
                try:
                    num_files = int(num_files_str) if num_files_str.isdigit() else num_map.get(num_files_str, 1)
                    print(f"    num_files: {num_files}")
                except ValueError:
                    num_files = 1
                    print(f"    num_files (error): {num_files}")
                
                # Test lines validation
                validated_lines = None
                if lines_str and lines_str.isdigit():
                    lines_count = int(lines_str)
                    if 5 <= lines_count <= 1000:  # Reasonable range
                        validated_lines = lines_count
                        print(f"    validated_lines: {validated_lines}")
                    else:
                        print(f"    lines_count {lines_count} outside reasonable range")
                else:
                    print(f"    No valid lines_str found")
                
                break
        else:
            print(f"  ❌ NO MATCH")
    
    # Test alternative patterns that might work better
    print(f"\n" + "="*50)
    print("TESTING ALTERNATIVE PATTERNS")
    print("="*50)
    
    alternative_patterns = [
        r'create\s+(\d+)\s+.*files?.*(\d+)\s+lines?',
        r'(\d+)\s+.*files?.*exactly\s+(\d+)\s+lines?',
        r'create\s+(\d+)\s+.*files?.*containing\s+exactly\s+(\d+)\s+lines?'
    ]
    
    for i, pattern in enumerate(alternative_patterns, 1):
        print(f"\nAlternative Pattern {i}: {pattern}")
        match = re.search(pattern, test_goal, re.IGNORECASE)
        if match:
            print(f"  ✅ MATCH: {match.groups()}")
        else:
            print(f"  ❌ NO MATCH")

if __name__ == "__main__":
    test_line_count_parsing()
