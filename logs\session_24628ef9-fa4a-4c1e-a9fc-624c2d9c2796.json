[{"event_id": "46630e06-730d-41b4-8e71-81f532f61196", "timestamp": "2025-06-08T15:03:09.792665", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "llm_query", "llm_query": {"model": "Mistral Small 3.1 (OpenRouter)", "prompt_length": 829, "temperature": null, "max_tokens": null, "has_system_prompt": false, "tools_available": 0}}, {"event_id": "ce3ca2c3-7a26-48fa-9738-40f279791bf8", "timestamp": "2025-06-08T15:03:11.459910", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "3964176d-0f50-485b-b3cd-9ce281e7ad7e", "timestamp": "2025-06-08T15:03:14.880873", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "66f63a69-62a7-4713-a1be-81bc57dc3ae9", "timestamp": "2025-06-08T15:03:20.619551", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "2db11c89-b829-4f70-851f-69751a165286", "timestamp": "2025-06-08T15:03:30.074217", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "cf1ec272-9065-463d-a696-094084cb53fd", "timestamp": "2025-06-08T15:03:48.454710", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "24357a3f-899b-4f83-a2dc-99fa51595586", "timestamp": "2025-06-08T15:04:21.862186", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "error_occurred", "error_details": {"component": "LLMInterface", "severity": "CRITICAL", "message": "Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions", "has_stack_trace": true}}, {"event_id": "c3646a65-220c-4ef0-90d7-219378b9e9a4", "timestamp": "2025-06-08T15:04:21.862757", "session_id": "24628ef9-fa4a-4c1e-a9fc-624c2d9c2796", "event_type": "llm_response", "llm_response": {"model": "Mistral Small 3.1 (OpenRouter)", "response_length": 0, "prompt_tokens": null, "completion_tokens": null, "total_tokens": null, "finish_reason": null, "latency_ms": null}, "metadata": {"error": "Final generation attempt failed: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions"}}]