#!/usr/bin/env python3
"""
Quick test to verify our fixes are working.
"""

import os
import sys

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def quick_test():
    """Quick test of our fixes."""
    print("\n" + "="*50)
    print("QUICK MULTI-FILE FIXES TEST")
    print("="*50)
    
    # Use provided API key
    openrouter_key = "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"

    if not openrouter_key:
        print("❌ No OpenRouter API key found")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Test just the planning phase first
    test_goal = "Create 3 Python files, each containing exactly 20 lines of code"
    
    print(f"\n🎯 Testing goal: {test_goal}")
    
    try:
        print("\n📋 Testing planning phase...")
        plan = agent.plan_once(test_goal)
        
        print(f"   Generated {len(plan)} actions:")
        create_file_count = 0
        for i, action in enumerate(plan, 1):
            print(f"   {i}. {action.tool_name}")
            if action.tool_name == 'create_file':
                create_file_count += 1
                target_lines = action.parameters.get('target_line_count', 'Not specified')
                print(f"      Target lines: {target_lines}")
        
        print(f"\n📊 Planning Results:")
        print(f"   create_file actions: {create_file_count}/3")
        print(f"   Planning success: {'✅' if create_file_count == 3 else '❌'}")
        
        if create_file_count == 3:
            print(f"\n🎉 FILE COUNT FIX WORKING!")

            # Now test execution to see line count enforcement
            print(f"\n⚡ Testing execution phase...")
            response = agent.batch_execute_plan(plan)
            print(f"   Execution status: {response.status}")

            # Check created files
            created_files = [f for f in os.listdir('.') if f.startswith('file') and f.endswith('.py')]
            print(f"\n📁 Files created: {len(created_files)}")

            line_counts = []
            for file in created_files:
                if os.path.exists(file):
                    with open(file, 'r') as f:
                        lines = len(f.readlines())
                    line_counts.append(lines)
                    print(f"   {file}: {lines} lines")

            # Check line count enforcement
            line_count_perfect = all(lines == 20 for lines in line_counts)
            line_count_close = all(18 <= lines <= 22 for lines in line_counts)

            print(f"\n📊 Line Count Results:")
            print(f"   Perfect (20 lines each): {line_count_perfect}")
            print(f"   Close (18-22 lines each): {line_count_close}")

            if line_count_perfect:
                print(f"\n🎉 BOTH FIXES WORKING PERFECTLY!")
            elif line_count_close:
                print(f"\n✅ FILE COUNT FIXED, LINE COUNT VERY CLOSE!")
            else:
                print(f"\n⚠️ FILE COUNT FIXED, LINE COUNT NEEDS MORE WORK")

            return True
        else:
            print(f"\n⚠️ File count fix needs more work")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
