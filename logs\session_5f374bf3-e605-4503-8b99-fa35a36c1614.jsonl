{"event_id":"8acd51a3-e96a-4b34-8991-582f8e965102","timestamp":"2025-06-08T19:58:30.538938","session_id":"5f374bf3-e605-4503-8b99-fa35a36c1614","event_type":"llm_query","llm_query":{"model":"Mistral Small 3.1 (OpenRouter)","prompt_length":829,"temperature":null,"max_tokens":null,"has_system_prompt":false,"tools_available":0}}
{"event_id":"ccd4d44e-7a8e-41e0-a87c-11ff3abf6987","timestamp":"2025-06-08T19:58:32.280666","session_id":"5f374bf3-e605-4503-8b99-fa35a36c1614","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
{"event_id":"3bbf48b0-ab3d-4889-83d2-0efd04b86e85","timestamp":"2025-06-08T19:58:36.189276","session_id":"5f374bf3-e605-4503-8b99-fa35a36c1614","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
{"event_id":"464b10e5-60f8-425e-bbab-2c5e4dfeadae","timestamp":"2025-06-08T19:58:42.085337","session_id":"5f374bf3-e605-4503-8b99-fa35a36c1614","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
{"event_id":"1118a905-7519-40d8-b22b-f6f48125da24","timestamp":"2025-06-08T19:58:52.183904","session_id":"5f374bf3-e605-4503-8b99-fa35a36c1614","event_type":"error_occurred","error_details":{"component":"LLMInterface","severity":"CRITICAL","message":"Generation Error: All LLM providers failed. Last error: 429 Client Error: Too Many Requests for url: https://openrouter.ai/api/v1/chat/completions","has_stack_trace":true}}
