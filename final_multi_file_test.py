#!/usr/bin/env python3
"""
Final test to verify multi-file creation is working correctly.
"""

import os
import sys

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def test_multi_file_creation():
    """Test multi-file creation with different scenarios."""
    print("\n" + "="*60)
    print("FINAL MULTI-FILE CREATION TEST")
    print("="*60)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    
    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent with OpenRouter")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Test cases
    test_cases = [
        {
            "name": "Simple 3-file request",
            "goal": "Create 3 Python files with basic functionality",
            "expected_files": 3
        },
        {
            "name": "Specific line count request", 
            "goal": "Create 2 Python files, each containing 30 lines of code",
            "expected_files": 2
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print(f"   Goal: {test_case['goal']}")
        
        # Clear existing test files
        for f in os.listdir('.'):
            if f.startswith('test_file') and f.endswith('.py'):
                os.remove(f)
        
        try:
            # Run the agent
            response = agent.run(test_case['goal'])
            
            # Count created files
            created_files = [f for f in os.listdir('.') if f.startswith('file') and f.endswith('.py')]
            created_count = len(created_files)
            
            print(f"   📁 Files created: {created_count}")
            for file in created_files:
                with open(file, 'r') as f:
                    lines = len(f.readlines())
                print(f"      {file}: {lines} lines")
            
            # Evaluate result
            if created_count >= test_case['expected_files']:
                print(f"   ✅ SUCCESS: Created {created_count} files (expected {test_case['expected_files']})")
                results.append(True)
            else:
                print(f"   ❌ PARTIAL: Created {created_count} files (expected {test_case['expected_files']})")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ FAILED: {e}")
            results.append(False)
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"   Tests passed: {sum(results)}/{len(results)}")
    print(f"   Success rate: {sum(results)/len(results)*100:.1f}%")
    
    if sum(results) >= len(results) * 0.5:  # At least 50% success
        print(f"   🎉 MULTI-FILE CREATION IS WORKING!")
        return True
    else:
        print(f"   ⚠️  Multi-file creation needs more work")
        return False

if __name__ == "__main__":
    success = test_multi_file_creation()
    sys.exit(0 if success else 1)
