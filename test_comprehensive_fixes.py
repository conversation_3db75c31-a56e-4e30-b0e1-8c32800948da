#!/usr/bin/env python3
"""
Comprehensive test to verify both file count and line count fixes.
"""

import os
import sys
import time

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import AgentOS
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def test_comprehensive_fixes():
    """Test both file count and line count fixes."""
    print("\n" + "="*70)
    print("COMPREHENSIVE MULTI-FILE CREATION FIXES TEST")
    print("="*70)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    
    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent with OpenRouter")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Clean up any existing test files
    for f in os.listdir('.'):
        if f.startswith('test_fix_') and f.endswith('.py'):
            os.remove(f)
            print(f"🗑️ Removed existing file: {f}")
    
    # Test case: Exact requirements
    test_goal = "Create 3 Python files, each containing exactly 25 lines of code"
    
    print(f"\n🎯 Testing goal: {test_goal}")
    print(f"   Expected: 3 files, each with exactly 25 lines")
    
    # Test the agent
    try:
        print("\n⚡ Running agent...")
        start_time = time.time()
        response = agent.run(test_goal)
        end_time = time.time()
        
        print(f"   Agent completed in {end_time - start_time:.1f} seconds")
        
        # Wait a moment for file system to sync
        time.sleep(1)
        
        # Check what files were created
        created_files = [f for f in os.listdir('.') if f.startswith('file') and f.endswith('.py')]
        print(f"\n📁 Files created: {len(created_files)}")
        
        total_lines = 0
        line_counts = []
        
        for file in created_files:
            if os.path.exists(file):
                with open(file, 'r') as f:
                    lines = len(f.readlines())
                line_counts.append(lines)
                total_lines += lines
                print(f"   {file}: {lines} lines")
        
        # Evaluate results
        print(f"\n📊 DETAILED RESULTS:")
        print(f"   Files created: {len(created_files)}/3")
        print(f"   Line counts: {line_counts}")
        print(f"   Average lines per file: {total_lines/len(created_files) if created_files else 0:.1f}")
        print(f"   Target lines per file: 25")
        
        # Success criteria
        file_count_perfect = len(created_files) == 3
        line_count_perfect = all(lines == 25 for lines in line_counts)
        line_count_close = all(23 <= lines <= 27 for lines in line_counts)  # Allow 2-line tolerance
        
        print(f"\n✅ EVALUATION:")
        print(f"   Perfect file count (3/3): {file_count_perfect}")
        print(f"   Perfect line counts (25 each): {line_count_perfect}")
        print(f"   Close line counts (23-27 each): {line_count_close}")
        
        # Overall success determination
        if file_count_perfect and line_count_perfect:
            print(f"\n🎉 PERFECT SUCCESS: All fixes working flawlessly!")
            return True
        elif file_count_perfect and line_count_close:
            print(f"\n✅ EXCELLENT SUCCESS: File count perfect, line counts very close!")
            return True
        elif file_count_perfect:
            print(f"\n⚠️ GOOD PROGRESS: File count fixed, line count needs refinement")
            print(f"   Line count accuracy: {sum(1 for lines in line_counts if 23 <= lines <= 27)}/{len(line_counts)} files")
            return True
        else:
            print(f"\n❌ NEEDS MORE WORK: File count still incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Agent execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_planning_only():
    """Test just the planning phase to see if the issue is in planning or execution."""
    print(f"\n🔍 PLANNING-ONLY TEST")
    print("-" * 40)
    
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    if not openrouter_key:
        print("❌ No OpenRouter API key found")
        return False
    
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        
        test_goal = "Create 3 Python files, each containing exactly 25 lines of code"
        print(f"Planning for: {test_goal}")
        
        plan = agent.plan_once(test_goal)
        
        print(f"\n📋 Generated plan with {len(plan)} actions:")
        create_file_count = 0
        for i, action in enumerate(plan, 1):
            print(f"  {i}. {action.tool_name}")
            if action.tool_name == 'create_file':
                create_file_count += 1
                target_lines = action.parameters.get('target_line_count', 'Not specified')
                print(f"     Target lines: {target_lines}")
        
        print(f"\n📊 Planning Analysis:")
        print(f"   Total create_file actions: {create_file_count}")
        print(f"   Expected create_file actions: 3")
        print(f"   Planning success: {create_file_count == 3}")
        
        return create_file_count == 3
        
    except Exception as e:
        print(f"❌ Planning test failed: {e}")
        return False

if __name__ == "__main__":
    print("Starting comprehensive fixes test...")
    
    # Test planning first
    planning_success = test_planning_only()
    
    # Then test full execution
    execution_success = test_comprehensive_fixes()
    
    print(f"\n" + "="*70)
    print("FINAL SUMMARY")
    print("="*70)
    print(f"Planning phase: {'✅ SUCCESS' if planning_success else '❌ FAILED'}")
    print(f"Execution phase: {'✅ SUCCESS' if execution_success else '❌ FAILED'}")
    
    if planning_success and execution_success:
        print(f"🎉 ALL FIXES WORKING CORRECTLY!")
        sys.exit(0)
    elif planning_success:
        print(f"⚠️ Planning fixed, execution needs work")
        sys.exit(1)
    else:
        print(f"❌ Both planning and execution need fixes")
        sys.exit(1)
