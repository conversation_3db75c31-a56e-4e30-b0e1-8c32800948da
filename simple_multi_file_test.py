#!/usr/bin/env python3
"""
Simple test to verify multi-file creation is working.
"""

import os
import sys

# Add the current directory to the path so we can import mindlink
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from mindlink.agent import Agent<PERSON>
    from mindlink.models.openrouter import OpenRouterModel
    from mindlink.config import DEFAULT_SYSTEM_PROMPT
    print("✅ Successfully imported MindLink components")
except ImportError as e:
    print(f"❌ Failed to import MindLink components: {e}")
    sys.exit(1)

def test_multi_file_creation():
    """Test multi-file creation."""
    print("\n" + "="*60)
    print("TESTING MULTI-FILE CREATION")
    print("="*60)
    
    # Check for API keys
    openrouter_key = os.getenv("OPENROUTER_API_KEY")
    
    if not openrouter_key:
        print("❌ No OpenRouter API key found. Please set OPENROUTER_API_KEY")
        return False

    # Initialize LLM and Agent
    try:
        llm = OpenRouterModel(api_key=openrouter_key, model_name="mistral-small-3.1")
        agent = AgentOS(llm=llm, system_prompt_template=DEFAULT_SYSTEM_PROMPT)
        print("🔗 Successfully initialized agent with OpenRouter")
    except Exception as e:
        print(f"❌ Failed to initialize agent: {e}")
        return False
    
    # Test case: Simple multi-file request
    test_goal = "Create 3 Python files, each containing 50 lines of functional code."
    
    print(f"\n🎯 Testing goal: {test_goal}")
    
    # Clear any existing files in D:/3/
    d3_dir = "D:/3"
    if os.path.exists(d3_dir):
        existing_files = [f for f in os.listdir(d3_dir) if f.endswith('.py')]
        print(f"📁 Existing Python files in D:/3/: {len(existing_files)}")
    
    # Test the agent
    try:
        print("\n⚡ Running agent...")
        response = agent.run(test_goal)
        print(f"   Agent response: {response.observation[:200]}...")
        
        # Check what files were created
        if os.path.exists(d3_dir):
            new_files = [f for f in os.listdir(d3_dir) if f.endswith('.py')]
            print(f"\n📁 Python files in D:/3/ after test: {len(new_files)}")
            
            for file in new_files:
                file_path = os.path.join(d3_dir, file)
                if os.path.exists(file_path):
                    with open(file_path, 'r') as f:
                        lines = len(f.readlines())
                    print(f"   {file}: {lines} lines")
            
            if len(new_files) >= 3:
                print(f"\n✅ SUCCESS: Created {len(new_files)} files (expected 3)")
                return True
            else:
                print(f"\n❌ PARTIAL: Created {len(new_files)} files (expected 3)")
                return False
        else:
            print(f"\n❌ FAILED: D:/3/ directory not found")
            return False
            
    except Exception as e:
        print(f"❌ Agent execution failed: {e}")
        return False

if __name__ == "__main__":
    success = test_multi_file_creation()
    sys.exit(0 if success else 1)
